package delayer

import (
	"assistantdeskgo/defines"

	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	jsoniter "github.com/json-iterator/go"
)

type NoticeCallTimeData struct {
	NoticeId int64 `json:"noticeId"`
	Type     int8  `json:"type"`
}

func (o *NoticeCallTimeData) Consumer(ctx *gin.Context, input DelayerTaskMessage) (err error) {
	data := &NoticeCallTimeData{}
	err = jsoniter.UnmarshalFromString(input.Content, data)
	if err != nil {
		zlog.Warnf(ctx, "notice delayer msg consumer failed, input:%+v, err: %+v", input, err)
		return err
	}

	if data.Type == defines.NoticeScheduleTypePub {

	}

	if data.Type == defines.NoticeScheduleTypeEnd {

	}

	return
}
